<template>
  <div class="main-layout">
    <!-- 侧边栏 -->
    <aside class="sidebar" :class="{ collapsed: appStore.sidebarCollapsed }">
      <SidebarNav
        :collapsed="appStore.sidebarCollapsed"
        @toggle-collapse="toggleSidebar"
        @menu-select="handleMenuSelect"
      />
    </aside>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 顶部导航栏 -->
      <header class="top-header">
        <div class="header-left">
          <el-button
            type="text"
            @click="toggleSidebar"
            class="toggle-btn"
            :icon="appStore.sidebarCollapsed ? Expand : Fold"
          />
          <span class="page-title">{{ appStore.currentPageTitle }}</span>
        </div>
        <div class="header-right">
          <el-dropdown @command="handleUserCommand" placement="bottom-end">
            <div class="user-info">
              <el-avatar :src="userStore.userInfo?.avatar" :size="32" class="user-avatar">
                {{ userStore.userInfo?.username?.charAt(0).toUpperCase() }}
              </el-avatar>
              <span class="username">{{ userStore.userInfo?.username }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  系统设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </header>

      <!-- 面包屑导航 -->
      <div v-if="appStore.breadcrumbItems.length > 0" class="breadcrumb-section">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item
            v-for="(item, index) in appStore.breadcrumbItems"
            :key="index"
            :to="item.route"
          >
            {{ item.title }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <!-- 页面内容区域 -->
      <div class="content-container">
        <router-view v-slot="{ Component, route }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive :include="['UsecaseLibraryNew']">
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </main>

    <!-- 全局加载遮罩 -->
    <el-loading
      v-loading="appStore.isLoading"
      element-loading-text="加载中..."
      element-loading-background="rgba(0, 0, 0, 0.7)"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  ElAvatar,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElIcon,
  ElBreadcrumb,
  ElBreadcrumbItem,
  ElLoading,
  ElMessageBox,
  ElMessage,
} from 'element-plus'
import { Expand, Fold, ArrowDown, User, Setting, SwitchButton } from '@element-plus/icons-vue'
import { useAppStore } from '../stores/app'
import { useUserStore } from '../stores/user'
import SidebarNav from '../components/SidebarNav.vue'

// 状态管理
const appStore = useAppStore()
const userStore = useUserStore()
const router = useRouter()

// 方法
const toggleSidebar = () => {
  appStore.setSidebarCollapsed(!appStore.sidebarCollapsed)
}

const handleMenuSelect = (menuKey: string) => {
  console.log('Menu selected:', menuKey)
  // 菜单选择逻辑
}

const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/app/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        userStore.logout()
        router.push('/login')
      } catch {
        // 用户取消操作
      }
      break
  }
}

// 监听键盘快捷键
const handleKeyDown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + B 切换侧边栏
  if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
    event.preventDefault()
    toggleSidebar()
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
  // 初始化面包屑
  appStore.setBreadcrumb([{ title: '首页', route: '/' }])
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped lang="scss">
.main-layout {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #f0f2f0 0%, #e8ebe8 100%);

  .sidebar {
    width: 240px;
    flex-shrink: 0;
    background: linear-gradient(180deg, #6b7a6e 0%, #5a6b5d 100%);
    transition: all 0.3s ease;
    z-index: 1010;
    box-shadow: 2px 0 8px rgba(105, 123, 107, 0.15);
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    overflow: hidden;

    &.collapsed {
      width: 64px;
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    margin-left: 240px;
    transition: margin-left 0.3s ease;

    .top-header {
      height: 60px;
      background: #fff;
      border-bottom: 1px solid #e4e7ed;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;

        .toggle-btn {
          color: #606266;
          font-size: 16px;

          &:hover {
            color: #409eff;
          }
        }

        .page-title {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
        }
      }

      .header-right {
        display: flex;
        align-items: center;
        gap: 16px;

        .user-info {
          display: flex;
          align-items: center;
          gap: 8px;
          cursor: pointer;
          padding: 8px;
          border-radius: 4px;
          transition: background-color 0.3s;

          &:hover {
            background: #f5f7fa;
          }

          .user-avatar {
            background: #409eff;
            color: #fff;
          }

          .username {
            font-size: 14px;
            color: #303133;
          }

          .dropdown-icon {
            font-size: 12px;
            color: #909399;
            transition: transform 0.3s;
          }

          &:hover .dropdown-icon {
            transform: rotate(180deg);
          }
        }
      }
    }

    .breadcrumb-section {
      background: #fff;
      padding: 12px 20px;
      border-bottom: 1px solid #e4e7ed;
    }

    .content-container {
      flex: 1;
      padding: 0;
      background: #f0f2f5;
      min-height: 0;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
}

// 页面切换动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

// 响应式设计
@media (max-width: 768px) {
  .main-layout {
    .sidebar {
      transform: translateX(-100%);

      &:not(.collapsed) {
        transform: translateX(0);
      }
    }

    .main-content {
      margin-left: 0;
    }
  }
}

// 侧边栏收起状态下的主内容区域调整
.main-layout {
  .sidebar.collapsed ~ .main-content {
    margin-left: 64px;
  }
}

@media (max-width: 1024px) {
  .main-layout {
    .sidebar {
      width: 200px;
    }
  }
}
</style>
