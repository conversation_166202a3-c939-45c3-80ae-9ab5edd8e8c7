<template>
  <div class="usecase-report-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">用例报告</h1>
        <p class="page-description">生成和管理用例测试报告</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showGenerateDialog" :loading="generating">
          <el-icon><DocumentAdd /></el-icon>
          生成用例报告
        </el-button>
        <el-button @click="refreshReportList" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新列表
        </el-button>
      </div>
    </div>

    <!-- 报告列表 -->
    <div class="report-list-section">
      <div class="section-header">
        <h2>已生成的用例报告</h2>
        <div class="section-info">
          <el-tag v-if="reportList.length > 0">共 {{ reportList.length }} 个报告</el-tag>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 空状态 -->
      <el-empty v-else-if="reportList.length === 0" description="暂无报告数据">
        <el-button type="primary" @click="showGenerateDialog">生成第一个报告</el-button>
      </el-empty>

      <!-- 报告列表 -->
      <div v-else class="report-list">
        <el-table :data="reportList" style="width: 100%">
          <el-table-column prop="name" label="报告名称" min-width="200">
            <template #default="{ row }">
              <div class="report-name-cell">
                <span class="report-name">{{ row.name }}</span>
                <span class="report-type">{{ row.type }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="metadata.test_cases_count" label="测试用例数量" width="120" align="center" />

          <el-table-column label="操作" width="120" align="center">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="downloadReport(row.export_id, row.name)"
                :loading="downloadingReports.has(row.export_id)"
              >
                <el-icon><Download /></el-icon>
                下载
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 生成报告对话框 -->
    <el-dialog
      v-model="generateDialogVisible"
      title="生成用例报告"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="generate-dialog-content">
        <div class="dialog-description">
          <p>请选择要生成报告的系统：</p>
        </div>

        <!-- 系统加载状态 -->
        <div v-if="loadingSystems" class="systems-loading">
          <el-skeleton :rows="3" animated />
        </div>

        <!-- 系统选择 -->
        <div v-else-if="systemList.length > 0" class="systems-selection">
          <el-radio-group v-model="selectedSystem" class="system-radio-group">
            <el-radio
              v-for="system in systemList"
              :key="system.name"
              :label="system.name"
              class="system-radio"
            >
              <div class="system-info">
                <div class="system-name">{{ system.name }}</div>
                <div class="system-details">
                  <span>{{ system.data.softwareName }}</span>
                  <span v-if="system.data.version">v{{ system.data.version }}</span>
                </div>
              </div>
            </el-radio>
          </el-radio-group>
        </div>

        <!-- 无系统数据 -->
        <el-empty v-else description="暂无系统数据" />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="generateDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="generateReport"
            :disabled="!selectedSystem"
            :loading="generating"
          >
            确定生成
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  ElButton,
  ElIcon,
  ElTag,
  ElSkeleton,
  ElEmpty,
  ElDialog,
  ElRadioGroup,
  ElRadio,
  ElMessage,
  ElMessageBox,
  ElTable,
  ElTableColumn
} from 'element-plus'
import {
  DocumentAdd,
  Refresh,
  Download
} from '@element-plus/icons-vue'
import { apiService } from '@/services/api'
import type {
  ReportListItem,
  ReportSystemsResponse,
  GenerateReportRequest
} from '@/services/api'

// 响应式数据
const loading = ref(false)
const generating = ref(false)
const loadingSystems = ref(false)
const generateDialogVisible = ref(false)
const selectedSystem = ref('')
const downloadingReports = ref(new Set<string>())

// 报告列表
const reportList = ref<ReportListItem[]>([])

// 系统列表
const systemList = ref<Array<{
  name: string
  data: any
}>>([])

// 获取报告列表
const fetchReportList = async () => {
  try {
    loading.value = true
    const response = await apiService.reports.getReportList({
      export_type: '测试用例',
      page: 1,
      page_size: 20,
      order_direction: 'desc'
    })

    console.log('报告列表API响应:', response)

    if (response.status === 'success') {
      reportList.value = response.data
    } else {
      throw new Error(response.message || '获取报告列表失败')
    }
  } catch (error: any) {
    console.error('获取报告列表失败:', error)
    ElMessage.error('获取报告列表失败：' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 获取系统列表
const fetchSystemList = async () => {
  try {
    loadingSystems.value = true
    const response = await apiService.reports.getSystems()

    console.log('系统列表API响应:', response)

    if (response.status === 'success') {
      // 转换系统数据格式
      systemList.value = Object.entries(response.data).map(([name, systemData]) => ({
        name,
        data: systemData.data
      }))
      console.log('转换后的系统列表:', systemList.value)
    } else {
      throw new Error(response.message || '获取系统列表失败')
    }
  } catch (error: any) {
    console.error('获取系统列表失败:', error)
    ElMessage.error('获取系统列表失败：' + (error.message || '未知错误'))
  } finally {
    loadingSystems.value = false
  }
}

// 显示生成对话框
const showGenerateDialog = async () => {
  generateDialogVisible.value = true
  selectedSystem.value = ''
  await fetchSystemList()
}

// 生成报告
const generateReport = async () => {
  if (!selectedSystem.value) {
    ElMessage.warning('请选择要生成报告的系统')
    return
  }

  try {
    generating.value = true
    const requestData: GenerateReportRequest = {
      name: selectedSystem.value,
      type: '测试用例'
    }

    const response = await apiService.reports.generateTestCaseReport(requestData)

    if (response.status === 'success') {
      ElMessage.success('测试用例报告生成成功')
      generateDialogVisible.value = false
      selectedSystem.value = ''
      // 刷新报告列表
      await fetchReportList()
    } else {
      throw new Error(response.message || '生成报告失败')
    }
  } catch (error: any) {
    console.error('生成报告失败:', error)
    ElMessage.error('生成报告失败：' + (error.message || '未知错误'))
  } finally {
    generating.value = false
  }
}

// 下载报告
const downloadReport = async (exportId: string, reportName: string) => {
  try {
    downloadingReports.value.add(exportId)

    const blob = await apiService.reports.downloadReport(exportId)

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${reportName}_测试用例报告.docx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('报告下载成功')
  } catch (error: any) {
    console.error('下载报告失败:', error)
    ElMessage.error('下载报告失败：' + (error.message || '未知错误'))
  } finally {
    downloadingReports.value.delete(exportId)
  }
}

// 刷新报告列表
const refreshReportList = async () => {
  await fetchReportList()
}



// 获取状态类型
const getStatusType = (status: string): 'success' | 'warning' | 'danger' | 'info' => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'processing':
      return 'warning'
    case 'failed':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string): string => {
  switch (status) {
    case 'completed':
      return '已完成'
    case 'processing':
      return '处理中'
    case 'failed':
      return '失败'
    default:
      return '未知'
  }
}

// 组件挂载时获取数据
onMounted(async () => {
  await fetchReportList()
})
</script>

<style scoped>
.usecase-report-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 报告列表区域 */
.report-list-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #ebeef5;
}

.section-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.section-info {
  display: flex;
  gap: 12px;
}

/* 加载状态 */
.loading-container {
  padding: 24px;
}

/* 报告列表 */
.report-list {
  padding: 0;
}

.report-name-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.report-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.report-type {
  font-size: 12px;
  color: #909399;
}

/* 生成对话框 */
.generate-dialog-content {
  padding: 8px 0;
}

.dialog-description {
  margin-bottom: 16px;
}

.dialog-description p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.systems-loading {
  padding: 20px 0;
}

.systems-selection {
  max-height: 300px;
  overflow-y: auto;
}

.system-radio-group {
  width: 100%;
}

.system-radio {
  width: 100%;
  margin-right: 0;
  margin-bottom: 12px;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.system-radio:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.system-radio.is-checked {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.system-info {
  margin-left: 8px;
}

.system-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.system-details {
  font-size: 12px;
  color: #909399;
  display: flex;
  gap: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .usecase-report-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .report-grid {
    grid-template-columns: 1fr;
    padding: 16px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
