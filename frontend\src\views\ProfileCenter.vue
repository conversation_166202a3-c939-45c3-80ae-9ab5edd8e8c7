<template>
  <div class="profile-center">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">个人中心</h1>
      <p class="page-subtitle">管理您的个人信息和账户设置</p>
    </div>

    <div class="profile-container">
      <!-- 用户信息展示区域 -->
      <el-card class="user-info-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><User /></el-icon>
            <span>基本信息</span>
          </div>
        </template>

        <div class="user-info-content">
          <!-- 用户头像 -->
          <div class="avatar-section">
            <el-avatar :src="userStore.userInfo?.avatar" :size="80" class="user-avatar">
              {{ userStore.userInfo?.username?.charAt(0).toUpperCase() }}
            </el-avatar>
          </div>

          <!-- 用户基本信息 -->
          <div class="info-section">
            <el-form label-width="100px" class="user-form">
              <el-form-item label="用户名">
                <el-input :value="userStore.userInfo?.username" readonly class="readonly-input" />
              </el-form-item>

              <el-form-item label="角色">
                <el-tag
                  :type="getRoleTagType(userStore.userInfo?.role)"
                  size="large"
                  class="role-tag"
                >
                  {{ getRoleDisplayName(userStore.userInfo?.role) }}
                </el-tag>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-card>



      <!-- 密码修改区域 -->
      <el-card class="password-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Lock /></el-icon>
            <span>密码修改</span>
          </div>
        </template>

        <div class="password-content">
          <el-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            label-width="120px"
            class="password-form"
          >
            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="passwordForm.newPassword"
                type="password"
                placeholder="请输入新密码"
                show-password
                clearable
              />
              <div class="password-hint">密码长度不能少于7个字符</div>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                :loading="isChangingPassword"
                :disabled="!isPasswordFormValid"
                @click="handleChangePassword"
                class="change-password-btn"
              >
                {{ isChangingPassword ? '修改中...' : '修改密码' }}
              </el-button>
              <el-button @click="resetPasswordForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import { apiService } from '@/services/api'

// 页面标题设置
document.title = '个人中心 - 用例管理平台'

const userStore = useUserStore()
const router = useRouter()

// 表单引用
const passwordFormRef = ref<FormInstance>()

// 密码修改表单
const passwordForm = reactive({
  newPassword: '',
})

// 密码修改状态
const isChangingPassword = ref(false)

// 密码长度验证规则
const validatePassword = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请输入新密码'))
    return
  }

  if (value.length < 7) {
    callback(new Error('密码长度不能少于7个字符'))
    return
  }

  callback()
}

// 表单验证规则
const passwordRules: FormRules = {
  newPassword: [{ required: true, validator: validatePassword, trigger: 'blur' }],
}

// 计算属性：密码表单是否有效
const isPasswordFormValid = computed(() => {
  return passwordForm.newPassword && passwordForm.newPassword.length >= 7
})

// 获取角色标签类型
const getRoleTagType = (role: string | undefined) => {
  switch (role) {
    case 'admin':
      return 'danger'
    case 'user':
      return 'success'
    default:
      return 'info'
  }
}

// 获取角色显示名称
const getRoleDisplayName = (role: string | undefined) => {
  switch (role) {
    case 'admin':
      return '管理员'
    case 'user':
      return '普通用户'
    default:
      return '未知角色'
  }
}



// 处理密码修改
const handleChangePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    // 表单验证
    await passwordFormRef.value.validate()

    // 确认操作
    await ElMessageBox.confirm('修改密码后需要重新登录，确定要继续吗？', '确认修改', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    isChangingPassword.value = true

    // 调用密码修改API
    const response = await apiService.auth.changePassword({
      password: passwordForm.newPassword,
    })

    if (response.status === 'success') {
      // 使用API返回的消息，如果没有则使用默认消息
      const successMessage = response.message || '密码修改成功'
      ElMessage.success(`${successMessage}，请重新登录`)

      // 清除当前用户的token
      await userStore.logout()

      // 立即跳转到登录页面
      router.push('/login')
    }
  } catch (error: any) {
    if (error.message) {
      ElMessage.error(error.message)
    } else if (error !== 'cancel') {
      ElMessage.error('密码修改失败，请重试')
    }
  } finally {
    isChangingPassword.value = false
  }
}

// 重置密码表单
const resetPasswordForm = () => {
  passwordForm.newPassword = ''
  passwordFormRef.value?.clearValidate()
}

// 页面初始化
onMounted(() => {
  // 检查用户是否已登录
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
  }
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/theme.scss' as theme;

.profile-center {
  padding: 24px;
  background: linear-gradient(
    135deg,
    theme.$color-background 0%,
    theme.$color-background-dark 100%
  );
  min-height: calc(100vh - 60px);

  .page-header {
    margin-bottom: 32px;
    text-align: center;

    .page-title {
      font-size: 2rem;
      font-weight: 600;
      color: theme.$color-text-primary;
      margin: 0 0 8px 0;
    }

    .page-subtitle {
      font-size: 1rem;
      color: theme.$color-text-secondary;
      margin: 0;
    }
  }

  .profile-container {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto;
    gap: 24px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }

    .user-info-card {
      grid-column: 1 / 2;
      grid-row: 1;

      @media (max-width: 768px) {
        grid-column: 1;
        grid-row: auto;
      }
    }

    .password-card {
      grid-column: 2 / 3;
      grid-row: 1;

      @media (max-width: 768px) {
        grid-column: 1;
        grid-row: auto;
      }
    }
  }

  // 卡片样式
  :deep(.el-card) {
    background: theme.$color-container;
    border: 1px solid theme.$color-border;
    border-radius: theme.$border-radius-large;
    box-shadow: theme.$shadow-soft;
    transition: all theme.$transition-normal;

    &:hover {
      box-shadow: theme.$shadow-medium;
      transform: translateY(-2px);
    }

    .el-card__header {
      background: linear-gradient(135deg, theme.$color-primary-light 0%, theme.$color-primary 100%);
      border-bottom: 1px solid theme.$color-border;
      padding: 16px 20px;

      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        color: theme.$color-text-light;
        font-weight: 600;
        font-size: 1.1rem;

        .header-icon {
          font-size: 1.2rem;
        }
      }
    }

    .el-card__body {
      padding: 24px;
    }
  }

  // 用户信息卡片
  .user-info-content {
    .avatar-section {
      text-align: center;
      margin-bottom: 24px;

      .user-avatar {
        background: linear-gradient(
          135deg,
          theme.$color-primary 0%,
          theme.$color-primary-dark 100%
        );
        color: theme.$color-text-light;
        font-size: 2rem;
        font-weight: 600;
        box-shadow: theme.$shadow-medium;
      }
    }

    .info-section {
      .user-form {
        .readonly-input {
          :deep(.el-input__inner) {
            background-color: theme.$color-background;
            border-color: theme.$color-border;
            color: theme.$color-text-primary;
            cursor: default;
          }
        }

        .role-tag {
          font-weight: 600;
          padding: 8px 16px;
          font-size: 0.9rem;
        }
      }
    }
  }



  // 密码修改卡片
  .password-content {
    .password-form {
      .password-hint {
        font-size: 0.8rem;
        color: theme.$color-text-secondary;
        margin-top: 4px;
        line-height: 1.4;
      }

      .change-password-btn {
        background: linear-gradient(
          135deg,
          theme.$color-primary 0%,
          theme.$color-primary-dark 100%
        );
        border-color: theme.$color-primary;
        color: theme.$color-text-light;
        font-weight: 600;
        padding: 12px 24px;

        &:hover {
          background: linear-gradient(
            135deg,
            theme.$color-primary-light 0%,
            theme.$color-primary 100%
          );
          border-color: theme.$color-primary-light;
          transform: translateY(-1px);
          box-shadow: theme.$shadow-glow-primary;
        }

        &:active {
          transform: translateY(0);
        }

        &:disabled {
          background: theme.$color-secondary;
          border-color: theme.$color-secondary;
          color: theme.$color-text-disabled;
          transform: none;
          box-shadow: none;
        }
      }
    }
  }

  // 表单样式覆盖
  :deep(.el-form) {
    .el-form-item__label {
      color: theme.$color-text-primary;
      font-weight: normal;
      font-size: 0.95rem;
    }

    .el-input__inner {
      border-color: theme.$color-border;
      background-color: theme.$color-container;
      color: theme.$color-text-primary;

      &:focus {
        border-color: theme.$color-border-focus;
        box-shadow: 0 0 0 2px rgba(theme.$color-primary-accent, 0.2);
      }
    }

    .el-button {
      border-radius: theme.$border-radius-medium;
      font-weight: 500;
      transition: all theme.$transition-normal;
    }
  }

  // 标签样式覆盖
  :deep(.el-tag) {
    border-radius: theme.$border-radius-small;
    font-weight: 500;
  }
}
</style>
