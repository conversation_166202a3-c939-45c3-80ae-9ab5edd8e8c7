// 用户角色类型（与后端API保持一致）
export type UserRole = 'admin' | 'user' | 'guest'

export interface UserProfile {
  user_id: string
  username: string
  role: UserRole
  is_active: boolean
  created_at: string
  last_login: string | null
  assigned_systems: string[]
  max_concurrent_sessions: number
  avatar?: string // 用户头像URL，可选字段
}

// 导入通用类型
import type { ApiResponse } from './common'

// 登录响应数据结构
export interface LoginResponseData {
  user_info: UserProfile
  auth_token: string
  expires_at: string
}

// 完整的登录响应
export interface LoginResponse extends ApiResponse<LoginResponseData> {}

export interface LoginForm {
  username: string
  password: string
}

export interface TokenInfo {
  access_token: string
  refresh_token: string
  expires_at: string
  expires_in: number
  token_type: string
}

export interface RefreshTokenResponse {
  access_token: string
  expires_at: string
  expires_in: number
}

// 用户权限枚举
export enum Permission {
  // 统计模块
  VIEW_STATISTICS = 'view_statistics',

  // 管理库模块
  MANAGE_OUTLINE = 'manage_outline',
  MANAGE_USECASE = 'manage_usecase',
  MANAGE_REPORT = 'manage_report',

  // 评审模块
  REVIEW_OUTLINE = 'review_outline',
  REVIEW_USECASE = 'review_usecase',

  // AI模块
  AI_CONFIG = 'ai_config',
  AI_CHAT = 'ai_chat',

  // 报告编制
  REPORT_OUTLINE = 'report_outline',
  REPORT_USECASE = 'report_usecase',
  REPORT_TEST = 'report_test',

  // 追踪关系
  TRACKING_RELATION = 'tracking_relation',

  // 系统管理
  SYSTEM_ADMIN = 'system_admin',

  // 权限管理
  PERMISSION_MANAGEMENT = 'permission_management',
}

// 角色权限映射
export const RolePermissions: Record<UserRole, Permission[]> = {
  admin: [
    Permission.VIEW_STATISTICS,
    Permission.MANAGE_OUTLINE,
    Permission.MANAGE_USECASE,
    Permission.MANAGE_REPORT,
    Permission.REVIEW_OUTLINE,
    Permission.REVIEW_USECASE,
    Permission.AI_CONFIG,
    Permission.AI_CHAT,
    Permission.REPORT_OUTLINE,
    Permission.REPORT_USECASE,
    Permission.REPORT_TEST,
    Permission.TRACKING_RELATION,
    Permission.SYSTEM_ADMIN,
    Permission.PERMISSION_MANAGEMENT,
  ],
  user: [
    Permission.VIEW_STATISTICS,
    Permission.MANAGE_OUTLINE,
    Permission.MANAGE_USECASE,
    Permission.MANAGE_REPORT,
    Permission.REVIEW_OUTLINE,
    Permission.REVIEW_USECASE,
    Permission.AI_CONFIG,
    Permission.AI_CHAT,
    Permission.REPORT_OUTLINE,
    Permission.REPORT_USECASE,
    Permission.REPORT_TEST,
    Permission.TRACKING_RELATION,
  ],
  guest: [], // 保留guest以兼容，但无权限
}

// 用户管理相关类型定义（新增）
export interface UserManagementProfile {
  _id: string                    // MongoDB ObjectId
  username: string
  role: "admin" | "user"
  permissions: {
    write: Record<string, any>
    read: Record<string, any>
  }
  is_active: boolean
  created_at: string            // ISO格式时间戳
  updated_at: string            // ISO格式时间戳
  last_login: string | null     // ISO格式时间戳或null
}

// 导入通用BaseResponse类型
import type { BaseResponse } from './common'

export interface UserListResponse extends BaseResponse {
  data: {
    users: UserManagementProfile[]
    total: number
  }
}

export interface CreateUserRequest {
  username: string
  password: string
  role: "admin" | "user"
  is_active?: boolean
  permissions?: {
    write: Record<string, any>
    read: Record<string, any>
  }
}

export interface CreateUserResponse extends BaseResponse {
  data: {
    user: UserManagementProfile
  } | null
}
