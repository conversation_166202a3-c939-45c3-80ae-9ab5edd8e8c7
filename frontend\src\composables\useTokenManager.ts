import { ref, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { jwtDecode } from 'jwt-decode'
import { ElMessage } from 'element-plus'

interface TokenPayload {
  exp: number
  iat: number
  sub: string
  [key: string]: any
}

export function useTokenManager() {
  const userStore = useUserStore()

  // 刷新定时器
  const refreshTimer = ref<NodeJS.Timeout | null>(null)

  // 网络状态监听
  const isOnline = ref(navigator.onLine)

  // 是否正在刷新token
  const isRefreshing = ref(false)

  // 获取token过期时间
  const getTokenExpiration = (token: string): number => {
    try {
      const payload = jwtDecode<TokenPayload>(token)
      return payload.exp * 1000 // 转换为毫秒
    } catch (error) {
      console.warn('Failed to parse token expiration:', error)
      return 0
    }
  }

  // 检查token是否即将过期
  const isTokenExpiringSoon = (token: string, minutes: number = 5): boolean => {
    const expiration = getTokenExpiration(token)
    const now = Date.now()
    const timeUntilExpiration = expiration - now
    return timeUntilExpiration <= minutes * 60 * 1000
  }

  // 检查token是否过期
  const isTokenExpired = (token: string): boolean => {
    const expiration = getTokenExpiration(token)
    return Date.now() >= expiration
  }

  // 刷新token
  const refreshToken = async (): Promise<boolean> => {
    if (isRefreshing.value) return false

    isRefreshing.value = true

    try {
      const result = await userStore.refreshToken()

      if (result.success) {
        return true
      } else {
        await handleTokenExpired()
        return false
      }
    } catch (error) {
      console.warn('Token refresh failed:', error)
      await handleTokenExpired()
      return false
    } finally {
      isRefreshing.value = false
    }
  }

  // 处理token过期
  const handleTokenExpired = async () => {
    ElMessage.error('登录已过期，请重新登录')
    await userStore.logout()
    window.location.href = '/login'
  }

  // 启动token监控
  // TODO: 临时注释 - 每分钟token刷新检查
  const startTokenMonitoring = () => {
    /* TODO: 临时注释 - 每分钟token刷新检查
    // 清除之前的定时器
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value)
    }

    // 每分钟检查一次token状态
    refreshTimer.value = setInterval(async () => {
      const token = userStore.token

      if (!token) {
        clearInterval(refreshTimer.value!)
        return
      }

      // 检查token是否过期
      if (isTokenExpired(token)) {
        await handleTokenExpired()
        return
      }

      // 检查token是否即将过期（5分钟内）
      if (isTokenExpiringSoon(token, 5)) {
        await refreshToken()
      }
    }, 60000) // 每分钟检查一次
    */
  }

  // 停止token监控
  const stopTokenMonitoring = () => {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value)
      refreshTimer.value = null
    }
  }

  // 页面加载时预刷新token
  const preRefreshToken = async () => {
    const token = userStore.token

    if (!token) return

    // 如果token将在10分钟内过期，预刷新
    if (isTokenExpiringSoon(token, 10)) {
      await refreshToken()
    }
  }

  // 网络状态变化处理
  const handleNetworkChange = async () => {
    isOnline.value = navigator.onLine

    if (isOnline.value) {
      // 网络恢复时，检查token状态
      const token = userStore.token
      if (token) {
        if (isTokenExpired(token)) {
          await handleTokenExpired()
        } else if (isTokenExpiringSoon(token, 10)) {
          await refreshToken()
        }
      }

      // TODO: 临时注释 - 每分钟token刷新检查
      // 重新启动监控
      // startTokenMonitoring()
    } else {
      ElMessage.warning('网络连接丢失，请检查网络状态')

      // 网络断开时停止监控
      stopTokenMonitoring()
    }
  }

  // 初始化
  const init = async () => {
    // 页面加载时预刷新token
    await preRefreshToken()

    // TODO: 临时注释 - 每分钟token刷新检查
    // 启动token监控
    // if (userStore.isLoggedIn) {
    //   startTokenMonitoring()
    // }
  }

  // 组件挂载时的初始化
  onMounted(() => {
    // 监听网络状态变化
    window.addEventListener('online', handleNetworkChange)
    window.addEventListener('offline', handleNetworkChange)

    // 初始化token管理
    init()
  })

  // 组件卸载时的清理
  onUnmounted(() => {
    // 清理监听器
    window.removeEventListener('online', handleNetworkChange)
    window.removeEventListener('offline', handleNetworkChange)

    // 停止token监控
    stopTokenMonitoring()
  })

  return {
    isOnline,
    isRefreshing,
    getTokenExpiration,
    isTokenExpiringSoon,
    isTokenExpired,
    refreshToken,
    startTokenMonitoring,
    stopTokenMonitoring,
    preRefreshToken,
    handleNetworkChange,
    init,
  }
}
