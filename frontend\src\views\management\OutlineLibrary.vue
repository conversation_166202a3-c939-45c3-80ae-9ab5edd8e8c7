<template>
  <FullScreenLayout
    title="大纲库"
    primary-title="大纲管理"
    secondary-title="大纲详情"
    sidebar-title="操作面板"
    layout="triple"
  >
    <template #toolbar-actions>
      <el-button type="primary" @click="createOutline">
        <el-icon><Plus /></el-icon>
        新建大纲
      </el-button>
      <el-button @click="importOutlines">
        <el-icon><Upload /></el-icon>
        导入大纲
      </el-button>
      <el-button @click="exportOutlines">
        <el-icon><Download /></el-icon>
        导出大纲
      </el-button>
    </template>

    <template #primary>
      <div class="outline-management">
        <div class="outline-tree">
          <el-tree
            ref="outlineTreeRef"
            :data="outlineTreeData"
            :props="treeProps"
            node-key="id"
            :default-expand-all="false"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
            @node-contextmenu="handleNodeContextMenu"
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <span class="node-label">{{ node.label }}</span>
                <div class="node-actions">
                  <el-tag :type="getOutlineStatusType(data.status)" size="small">
                    {{ data.status }}
                  </el-tag>
                  <span class="node-count">{{ data.count || 0 }}</span>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>
    </template>

    <template #secondary>
      <div class="outline-details">
        <div v-if="selectedOutline" class="detail-content">
          <div class="detail-header">
            <h3>{{ selectedOutline.title }}</h3>
            <el-tag :type="getOutlineStatusType(selectedOutline.status)">
              {{ selectedOutline.status }}
            </el-tag>
          </div>

          <div class="detail-form">
            <el-form label-width="80px" size="small">
              <el-form-item label="大纲名称">
                <el-input v-model="selectedOutline.title" />
              </el-form-item>
              <el-form-item label="版本">
                <el-input v-model="selectedOutline.version" />
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model="selectedOutline.status">
                  <el-option label="草稿" value="draft" />
                  <el-option label="审核中" value="reviewing" />
                  <el-option label="已发布" value="published" />
                  <el-option label="已归档" value="archived" />
                </el-select>
              </el-form-item>
              <el-form-item label="负责人">
                <el-input v-model="selectedOutline.assignee" />
              </el-form-item>
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="selectedOutline.createTime"
                  type="datetime"
                  placeholder="选择时间"
                />
              </el-form-item>
              <el-form-item label="描述">
                <el-input
                  v-model="selectedOutline.description"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入描述..."
                />
              </el-form-item>
            </el-form>
          </div>

          <div class="detail-actions">
            <el-button type="primary" @click="saveOutline">
              <el-icon><Check /></el-icon>
              保存
            </el-button>
            <el-button @click="previewOutline">
              <el-icon><View /></el-icon>
              预览
            </el-button>
            <el-button @click="duplicateOutline">
              <el-icon><DocumentCopy /></el-icon>
              复制
            </el-button>
            <el-button type="danger" @click="deleteOutline">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </div>

        <div v-else class="no-selection">
          <el-icon><Document /></el-icon>
          <p>请在左侧选择一个大纲查看详情</p>
        </div>
      </div>
    </template>

    <template #sidebar>
      <div class="operation-panel">
        <!-- 搜索筛选 -->
        <div class="search-section">
          <h4>搜索筛选</h4>
          <el-form label-width="60px" size="small">
            <el-form-item label="关键词">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索大纲..."
                clearable
                @input="handleSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="filterStatus" placeholder="选择状态" clearable>
                <el-option label="草稿" value="draft" />
                <el-option label="审核中" value="reviewing" />
                <el-option label="已发布" value="published" />
                <el-option label="已归档" value="archived" />
              </el-select>
            </el-form-item>
            <el-form-item label="负责人">
              <el-select v-model="filterAssignee" placeholder="选择负责人" clearable>
                <el-option label="张三" value="zhangsan" />
                <el-option label="李四" value="lisi" />
                <el-option label="王五" value="wangwu" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="applyFilters"> 应用筛选 </el-button>
              <el-button size="small" @click="resetFilters"> 重置 </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 统计信息 -->
        <div class="stats-section">
          <h4>统计信息</h4>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ stats.totalOutlines }}</div>
              <div class="stat-label">总大纲数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ stats.publishedOutlines }}</div>
              <div class="stat-label">已发布</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ stats.draftOutlines }}</div>
              <div class="stat-label">草稿</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ stats.archivedOutlines }}</div>
              <div class="stat-label">已归档</div>
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="quick-actions">
          <h4>快速操作</h4>
          <div class="action-buttons">
            <el-button size="small" @click="createTemplate">
              <el-icon><Document /></el-icon>
              创建模板
            </el-button>
            <el-button size="small" @click="batchExport">
              <el-icon><Download /></el-icon>
              批量导出
            </el-button>
            <el-button size="small" @click="syncOutlines">
              <el-icon><Refresh /></el-icon>
              同步大纲
            </el-button>
            <el-button size="small" @click="validateOutlines">
              <el-icon><Check /></el-icon>
              验证大纲
            </el-button>
          </div>
        </div>

        <!-- 最近更新 -->
        <div class="recent-updates">
          <h4>最近更新</h4>
          <div class="update-list">
            <div v-for="update in recentUpdates" :key="update.id" class="update-item">
              <div class="update-title">{{ update.title }}</div>
              <div class="update-meta">
                <span class="update-author">{{ update.author }}</span>
                <span class="update-time">{{ update.time }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </FullScreenLayout>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import {
  ElButton,
  ElIcon,
  ElTree,
  ElTag,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
} from 'element-plus'
import {
  Plus,
  Upload,
  Download,
  Check,
  View,
  DocumentCopy,
  Delete,
  Document,
  Search,
  Refresh,
} from '@element-plus/icons-vue'
import FullScreenLayout from '@/components/ui/FullScreenLayout.vue'

// 大纲树数据
const outlineTreeData = ref([
  {
    id: 1,
    label: '用户管理系统大纲',
    status: 'published',
    count: 25,
    children: [
      {
        id: 2,
        label: '用户注册模块',
        status: 'published',
        count: 8,
        children: [
          { id: 3, label: '注册流程', status: 'published', count: 3 },
          { id: 4, label: '验证规则', status: 'draft', count: 5 },
        ],
      },
      {
        id: 5,
        label: '用户登录模块',
        status: 'reviewing',
        count: 12,
        children: [
          { id: 6, label: '登录验证', status: 'published', count: 6 },
          { id: 7, label: '权限控制', status: 'draft', count: 6 },
        ],
      },
    ],
  },
  {
    id: 8,
    label: '订单管理系统大纲',
    status: 'draft',
    count: 18,
    children: [
      { id: 9, label: '订单创建', status: 'draft', count: 10 },
      { id: 10, label: '订单处理', status: 'draft', count: 8 },
    ],
  },
])

// 树配置
const treeProps = {
  children: 'children',
  label: 'label',
}

// 大纲接口定义
interface OutlineItem {
  id: any
  title: any
  status: any
  version: string
  assignee: string
  createTime: Date
  description: string
}

// 选中的大纲
const selectedOutline = ref<OutlineItem | null>(null)

// 搜索和筛选
const searchKeyword = ref('')
const filterStatus = ref('')
const filterAssignee = ref('')

// 统计信息
const stats = reactive({
  totalOutlines: 43,
  publishedOutlines: 28,
  draftOutlines: 12,
  archivedOutlines: 3,
})

// 最近更新
const recentUpdates = ref([
  { id: 1, title: '用户登录模块大纲', author: '张三', time: '2小时前' },
  { id: 2, title: '订单处理流程大纲', author: '李四', time: '1天前' },
  { id: 3, title: '支付系统大纲', author: '王五', time: '2天前' },
])

// 方法
const createOutline = () => {
  console.log('创建大纲')
}

const importOutlines = () => {
  console.log('导入大纲')
}

const exportOutlines = () => {
  console.log('导出大纲')
}

const handleNodeClick = (data: any) => {
  selectedOutline.value = {
    id: data.id,
    title: data.label,
    status: data.status,
    version: '1.0',
    assignee: '张三',
    createTime: new Date(),
    description: '这是一个测试大纲的描述信息。',
  }
}

const handleNodeContextMenu = (event: Event, data: any) => {
  event.preventDefault()
  console.log('右键点击', data)
}

const getOutlineStatusType = (status: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    draft: 'info',
    reviewing: 'warning',
    published: 'success',
    archived: 'danger',
  }
  return statusMap[status] || 'info'
}

const saveOutline = () => {
  console.log('保存大纲')
}

const previewOutline = () => {
  console.log('预览大纲')
}

const duplicateOutline = () => {
  console.log('复制大纲')
}

const deleteOutline = () => {
  console.log('删除大纲')
}

const handleSearch = () => {
  console.log('搜索', searchKeyword.value)
}

const applyFilters = () => {
  console.log('应用筛选')
}

const resetFilters = () => {
  searchKeyword.value = ''
  filterStatus.value = ''
  filterAssignee.value = ''
}

const createTemplate = () => {
  console.log('创建模板')
}

const batchExport = () => {
  console.log('批量导出')
}

const syncOutlines = () => {
  console.log('同步大纲')
}

const validateOutlines = () => {
  console.log('验证大纲')
}
</script>

<style scoped lang="scss">
.outline-management {
  height: 100%;

  .outline-tree {
    height: 100%;
    overflow-y: auto;

    .tree-node {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      padding-right: 8px;

      .node-label {
        flex: 1;
        font-size: 14px;
      }

      .node-actions {
        display: flex;
        align-items: center;
        gap: 8px;

        .node-count {
          font-size: 12px;
          color: #909399;
          background: #f0f2f5;
          padding: 2px 6px;
          border-radius: 10px;
        }
      }
    }
  }
}

.outline-details {
  height: 100%;

  .detail-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #e4e7ed;

      h3 {
        margin: 0;
        font-size: 16px;
        color: #303133;
      }
    }

    .detail-form {
      flex: 1;
      overflow-y: auto;
      margin-bottom: 16px;
    }

    .detail-actions {
      display: flex;
      gap: 8px;
      padding-top: 12px;
      border-top: 1px solid #e4e7ed;

      .el-button {
        flex: 1;
      }
    }
  }

  .no-selection {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;

    .el-icon {
      font-size: 48px;
      margin-bottom: 12px;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

.operation-panel {
  .search-section,
  .stats-section,
  .quick-actions,
  .recent-updates {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;

    .stat-item {
      background: #f8f9fa;
      padding: 12px;
      border-radius: 6px;
      text-align: center;

      .stat-value {
        font-size: 18px;
        font-weight: 600;
        color: #409eff;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 11px;
        color: #909399;
      }
    }
  }

  .action-buttons {
    display: grid;
    gap: 8px;

    .el-button {
      justify-content: flex-start;
    }
  }

  .update-list {
    .update-item {
      padding: 8px 0;
      border-bottom: 1px solid #e4e7ed;

      &:last-child {
        border-bottom: none;
      }

      .update-title {
        font-size: 13px;
        color: #303133;
        margin-bottom: 4px;
      }

      .update-meta {
        display: flex;
        justify-content: space-between;
        font-size: 11px;
        color: #909399;
      }
    }
  }
}
</style>
