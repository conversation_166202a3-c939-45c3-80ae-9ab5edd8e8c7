<template>
  <FullScreenLayout title="AI对话" :show-default-toolbar="false" layout="single">
    <template #content>
      <div class="ai-chat-container">
        <div class="chat-area">
          <div class="chat-messages" ref="messagesContainer">
            <div v-for="message in messages" :key="message.id" :class="['message', message.type]">
              <div class="message-avatar">
                <el-icon v-if="message.type === 'user'"><User /></el-icon>
                <el-icon v-else><Robot /></el-icon>
              </div>
              <div class="message-content">
                <div class="message-text">{{ message.content }}</div>
                <div class="message-time">{{ message.time }}</div>
              </div>
            </div>
            <div v-if="loading" class="message ai loading">
              <div class="message-avatar">
                <el-icon><User /></el-icon>
              </div>
              <div class="message-content">
                <div class="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          </div>

          <div class="chat-input">
            <div class="input-area">
              <el-input
                v-model="inputMessage"
                type="textarea"
                :rows="3"
                placeholder="输入您的问题..."
                @keydown.ctrl.enter="sendMessage"
              />
              <div class="input-actions">
                <el-button @click="clearChat">
                  <el-icon><Delete /></el-icon>
                  清空对话
                </el-button>
                <el-button type="primary" @click="sendMessage" :loading="loading">
                  <el-icon><Promotion /></el-icon>
                  发送 (Ctrl+Enter)
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <div class="sidebar">
          <div class="quick-prompts">
            <h4>快速提示</h4>
            <div class="prompt-list">
              <div
                v-for="prompt in quickPrompts"
                :key="prompt.id"
                class="prompt-item"
                @click="usePrompt(prompt.content)"
              >
                {{ prompt.title }}
              </div>
            </div>
          </div>

          <div class="chat-history">
            <h4>对话历史</h4>
            <div class="history-list">
              <div
                v-for="chat in chatHistory"
                :key="chat.id"
                class="history-item"
                @click="loadChat(chat)"
              >
                <div class="history-title">{{ chat.title }}</div>
                <div class="history-time">{{ chat.time }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </FullScreenLayout>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { ElButton, ElIcon, ElInput } from 'element-plus'
import { User, Delete, Promotion } from '@element-plus/icons-vue'
import FullScreenLayout from '@/components/ui/FullScreenLayout.vue'

const inputMessage = ref('')
const loading = ref(false)
const messagesContainer = ref<HTMLElement>()

const messages = ref([
  {
    id: 1,
    type: 'ai',
    content:
      '您好！我是AI助手，可以帮助您解答关于测试用例设计、缺陷分析、测试策略等问题。请问有什么可以帮助您的吗？',
    time: '09:00',
  },
])

const quickPrompts = ref([
  {
    id: 1,
    title: '生成登录功能测试用例',
    content: '请为用户登录功能生成详细的测试用例，包括正常流程和异常流程',
  },
  { id: 2, title: '分析性能测试策略', content: '请帮我分析一个电商网站的性能测试策略' },
  { id: 3, title: 'API测试用例设计', content: '如何设计RESTful API的测试用例？' },
  { id: 4, title: '自动化测试框架选择', content: '对于Web应用，应该选择什么自动化测试框架？' },
])

const chatHistory = ref([
  { id: 1, title: '登录功能测试用例设计', time: '昨天 14:30' },
  { id: 2, title: 'API接口测试策略', time: '昨天 10:15' },
  { id: 3, title: '性能测试方案讨论', time: '2天前' },
])

const sendMessage = async () => {
  if (!inputMessage.value.trim() || loading.value) return

  const userMessage = {
    id: Date.now(),
    type: 'user',
    content: inputMessage.value,
    time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
  }

  messages.value.push(userMessage)
  inputMessage.value = ''
  loading.value = true

  await nextTick()
  scrollToBottom()

  // 模拟AI响应
  setTimeout(() => {
    const aiMessage = {
      id: Date.now() + 1,
      type: 'ai',
      content: '这是一个模拟的AI响应。在实际应用中，这里会连接到真实的AI服务。',
      time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
    }
    messages.value.push(aiMessage)
    loading.value = false
    nextTick(() => scrollToBottom())
  }, 2000)
}

const usePrompt = (content: string) => {
  inputMessage.value = content
}

const clearChat = () => {
  messages.value = []
}

const loadChat = (chat: any) => {
  console.log('加载对话', chat)
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}
</script>

<style scoped lang="scss">
.ai-chat-container {
  height: 100%;
  display: flex;
  gap: 20px;
  padding: 20px;

  .chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .chat-messages {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      min-height: 0;

      .message {
        display: flex;
        gap: 12px;
        margin-bottom: 20px;

        &.user {
          flex-direction: row-reverse;

          .message-content {
            background: #409eff;
            color: white;
          }
        }

        &.ai .message-content {
          background: #f0f2f5;
          color: #303133;
        }

        .message-avatar {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          background: #e4e7ed;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          .el-icon {
            font-size: 18px;
            color: #606266;
          }
        }

        .message-content {
          max-width: 70%;
          padding: 12px 16px;
          border-radius: 12px;
          word-wrap: break-word;

          .message-text {
            line-height: 1.5;
          }

          .message-time {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 4px;
          }
        }

        &.loading .message-content {
          padding: 16px;

          .typing-indicator {
            display: flex;
            gap: 4px;

            span {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background: #409eff;
              animation: typing 1.4s infinite ease-in-out;

              &:nth-child(1) {
                animation-delay: 0s;
              }
              &:nth-child(2) {
                animation-delay: 0.2s;
              }
              &:nth-child(3) {
                animation-delay: 0.4s;
              }
            }
          }
        }
      }
    }

    .chat-input {
      padding: 20px;
      border-top: 1px solid #e4e7ed;

      .input-area {
        .input-actions {
          display: flex;
          justify-content: space-between;
          margin-top: 12px;
        }
      }
    }
  }

  .sidebar {
    width: 280px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .quick-prompts,
    .chat-history {
      background: #fff;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      h4 {
        margin: 0 0 16px 0;
        font-size: 16px;
        color: #303133;
      }

      .prompt-list {
        .prompt-item {
          padding: 12px;
          margin-bottom: 8px;
          background: #f8f9fa;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          color: #606266;
          transition: all 0.3s;

          &:hover {
            background: #e4e7ed;
            color: #303133;
          }
        }
      }

      .history-list {
        .history-item {
          padding: 12px;
          margin-bottom: 8px;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            background: #f8f9fa;
          }

          .history-title {
            font-size: 14px;
            color: #303133;
            margin-bottom: 4px;
          }

          .history-time {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
}

@keyframes typing {
  0%,
  60%,
  100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

@media (max-width: 768px) {
  .ai-chat-container {
    flex-direction: column;

    .sidebar {
      width: 100%;
    }
  }
}
</style>
